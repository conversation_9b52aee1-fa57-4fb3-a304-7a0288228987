<template>
  <div class="h-full flex flex-col">
    <div class="mt4 h-0 flex flex-1 flex-col bg-bg-container p4">
      <c-pro-table
        ref="tableRef"
        :api="getTemplateList"
        :columns="columns"
        :serial-number="true"
        row-key="id"
        immediate
        bordered
      >
        <template #header>
          <a-button type="primary" @click="openEditModal()">新增模板</a-button>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'action'">
            <a @click="openEditModal(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="previewTemplate(record)">预览</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除该模板？" @confirm="deleteTemplate(record)">
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </c-pro-table>
    </div>
    <!-- 新增/编辑弹窗 -->
    <c-modal v-model:open="editVisible" title="图表模板编辑" width="1200px" @ok="handleEditOk" @cancel="editVisible = false">
      <a-card size="small" class="mb-2">
        <a-form :model="editForm" layout="inline" class="w-full">
          <a-form-item label="模板名称" required class="w-full">
            <a-input v-model:value="editForm.name" class="w-full" placeholder="请输入模板名称，如：折线图、柱状图等" />
          </a-form-item>
        </a-form>
      </a-card>
      <a-tabs v-model:active-key="activeTab" class="mt-2">
        <a-tab-pane key="config" tab="配置信息">
          <div class="h-96 flex gap4">
            <!-- 左侧：ECharts配置模板 -->
            <div class="flex flex-1 flex-col">
              <div class="mb2 font-medium">ECharts配置模板</div>
              <a-textarea
                v-model:value="editForm.optionTemplate!"
                placeholder="请输入ECharts配置模板（支持Scriban语法）"
                :rows="20"
                class="flex-1 font-mono"
              />
            </div>

            <!-- 右侧：字段配置 -->
            <div class="flex flex-1 flex-col">
              <div class="mb2 flex items-center justify-between">
                <span class="font-medium">字段配置</span>
                <a-space size="small">
                  <a-button size="small" @click="generateFieldsFromTemplate">
                    <template #icon>
                      <c-icon-thunderbolt-outlined />
                    </template>
                    智能生成
                  </a-button>
                  <a-button type="primary" size="small" @click="addFieldMapping">
                    <template #icon>
                      <c-icon-plus-outlined />
                    </template>
                    添加字段
                  </a-button>
                </a-space>
              </div>

              <!-- 字段树形结构 -->
              <div class="flex-1 overflow-auto border border-gray-200 rounded p2">
                <a-tree
                  v-if="fieldMappings.length > 0"
                  ref="fieldTreeRef"
                  :tree-data="fieldMappings"
                  :field-names="{ children: 'children', title: 'displayName', key: 'key' }"
                  :default-expand-all="true"
                  :expanded-keys="expandedKeys"
                  :show-line="{ showLeafIcon: false }"
                  class="custom-tree w-full"
                  @expand="onTreeExpand"
                >
                  <template #icon="{ data }">
                    <span class="field-icon">
                      <c-icon-setting-outlined v-if="data.configType === ConfigType.StyleConfig" />
                      <c-icon-apartment-outlined v-else-if="canHaveChildren(data)" />
                      <c-icon-tag-outlined v-else />
                    </span>
                  </template>
                  <template #title="{ data }">
                    <div class="field-node w-full" @click="selectField(data)">
                      <div class="w-full flex items-center justify-between">
                        <div class="flex flex-1 items-center gap2">
                          <span class="text-blue-600 font-medium">{{ data.displayName || '未命名字段' }}</span>
                          <a-tag size="small" :color="getDataTypeColor(data.dataType)">
                            {{ getDataTypeLabel(data.dataType) }}
                          </a-tag>
                          <a-tag v-if="!isChildField(data)" size="small" :color="getConfigTypeColor(data.configType)">
                            {{ getConfigTypeLabel(data.configType) }}
                          </a-tag>
                        </div>
                        <div class="flex items-center gap1">
                          <a-button
                            v-if="canHaveChildren(data)"
                            type="text"
                            size="small"
                            @click.stop="addChildField(data)"
                          >
                            <template #icon>
                              <c-icon-plus-outlined />
                            </template>
                          </a-button>
                          <a-button
                            type="text"
                            size="small"
                            danger
                            @click.stop="removeFieldMapping(data)"
                          >
                            <template #icon>
                              <c-icon-delete-outlined />
                            </template>
                          </a-button>
                        </div>
                      </div>
                      <div class="mt1 text-xs text-gray-500">
                        字段名: {{ data.name || '未设置' }}
                      </div>
                    </div>
                  </template>
                </a-tree>
                <div v-else class="py8 text-center text-gray-400">
                  暂无字段配置，点击上方"添加字段"按钮开始配置
                </div>
              </div>
            </div>
          </div>

          <!-- 字段详细配置面板 -->
          <a-card v-if="selectedField" size="small" class="mt4" title="字段详细配置">
            <a-form :model="selectedField" layout="vertical">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="字段名称" required>
                    <a-input
                      v-model:value="selectedField.name"
                      placeholder="字段名称，如：XField"
                      @change="updateFieldDisplay"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="显示名称" required>
                    <a-input
                      v-model:value="selectedField.displayName"
                      placeholder="显示名称，如：X轴字段"
                      @change="updateFieldDisplay"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="数据类型">
                    <a-select
                      v-model:value="selectedField.dataType"
                      @change="onDataTypeChange(selectedField)"
                    >
                      <a-select-option :value="FieldDataType.String">字符串</a-select-option>
                      <a-select-option :value="FieldDataType.Number">数字</a-select-option>
                      <a-select-option :value="FieldDataType.Boolean">布尔值</a-select-option>
                      <a-select-option :value="FieldDataType.StringArray">字符串数组</a-select-option>
                      <a-select-option :value="FieldDataType.NumberArray">数字数组</a-select-option>
                      <a-select-option :value="FieldDataType.ObjectArray">对象数组</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col v-if="!isChildField(selectedField)" :span="8">
                  <a-form-item label="配置类型">
                    <a-select v-model:value="selectedField.configType">
                      <a-select-option :value="ConfigType.FieldMapping">字段映射</a-select-option>
                      <a-select-option :value="ConfigType.StyleConfig">样式配置</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="16">
                  <a-form-item label="字段说明">
                    <a-input
                      :value="selectedField.displayName || ''"
                      placeholder="详细说明该字段的用途和含义"
                      disabled
                    />
                    <div class="mt1 text-xs text-gray-500">
                      提示：字段说明会显示在字段配置界面中，帮助用户理解字段用途
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>
        </a-tab-pane>

        <a-tab-pane key="preview" tab="预览效果">
          <div class="h-96 flex gap4">
            <!-- 左侧：预览配置 -->
            <div class="w-80 flex flex-col">
              <div class="mb2 font-medium">预览配置</div>
              <a-card size="small" class="flex-1">
                <a-form layout="vertical" size="small">
                  <a-form-item label="生成示例数据">
                    <a-button type="primary" size="small" :loading="generatingData" class="w-full" @click="generatePreviewData">
                      <template #icon>
                        <c-icon-thunderbolt-outlined />
                      </template>
                      生成数据
                    </a-button>
                  </a-form-item>
                  <a-form-item label="预览数据">
                    <a-textarea
                      v-model:value="previewDataJson"
                      placeholder="JSON格式的预览数据"
                      :rows="8"
                      class="text-xs font-mono"
                    />
                  </a-form-item>
                  <a-form-item label="样式配置">
                    <a-textarea
                      v-model:value="previewConfigJson"
                      placeholder="JSON格式的样式配置"
                      :rows="6"
                      class="text-xs font-mono"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button type="primary" size="small" :loading="updatingPreview" @click="updatePreview">
                      <template #icon>
                        <c-icon-eye-outlined />
                      </template>
                      更新预览
                    </a-button>
                  </a-form-item>
                </a-form>
              </a-card>
            </div>

            <!-- 右侧：图表预览区域 -->
            <div class="flex-1 border border-gray-200 rounded">
              <ChartRenderer
                ref="chartRendererRef"
                :config="renderedChartConfig"
                class="h-full w-full"
              />
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </c-modal>
  </div>
</template>

<script lang="ts" setup>
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import { ChartStatistices } from '@/.generated/apis'
import { ChartTemplate } from '@/.generated/models/ChartTemplate'
import { ConfigType } from '@/.generated/models/ConfigType'
import { FieldDataType } from '@/.generated/models/FieldDataType'
import { FieldMapping } from '@/.generated/models/FieldMapping'
import ChartRenderer from '@/components/ChartRenderer.vue'
import { Guid } from '@/utils/GUID'
import { message, Modal } from 'ant-design-vue'
import { computed, nextTick, ref } from 'vue'

definePage({
  meta: {
    layout: 'admin',
    title: '图表模板',
    local: true,
    icon: 'BarChartOutlined',
    order: 1,
  },
})

const tableRef = useTemplateRef('tableRef')

const columns = ref<ColumnProps[]>([
  { title: '模板名称', dataIndex: 'name', key: 'name', search: {
    el: 'input',
    method: 'GET',
    attrs: { placeholder: '请输入模板名称' },
  } },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '创建时间', dataIndex: 'updatedAt', dateFormat: true, key: 'updatedAt' },
  { title: '操作', key: 'action', width: 150 },
])

async function getTemplateList(params: any) {
  const res = await ChartStatistices.GetTemplatesAsync(params)
  return {
    items: res.items,
    totals: res.totals,
    offset: params.offset,
    limit: params.limit,
  }
}

const editVisible = ref(false)
const activeTab = ref('config')
const editForm = ref<Partial<ChartTemplate>>({})

// 扩展的字段映射接口，用于树形表格
interface FieldMappingWithKey extends FieldMapping {
  key: string
  children?: FieldMappingWithKey[]
}

// 直接使用带key的数据结构，支持双向绑定
const fieldMappings = ref<FieldMappingWithKey[]>([])

// 当前选中的字段
const selectedField = ref<FieldMappingWithKey | null>(null)

// 树组件引用
const fieldTreeRef = useTemplateRef('fieldTreeRef')

// 图表渲染器引用
const chartRendererRef = useTemplateRef('chartRendererRef')

// 展开的节点keys
const expandedKeys = ref<string[]>([])

// 预览相关状态
const previewDataJson = ref<string>('')
const previewConfigJson = ref<string>('')
const generatingData = ref(false)
const updatingPreview = ref(false)

// 渲染后的图表配置
const renderedChartConfig = computed(() => {
  if (!editForm.value.optionTemplate) {
    console.log('预览失败: 缺少配置模板')
    return null
  }

  if (!previewDataJson.value) {
    console.log('预览失败: 缺少预览数据')
    return null
  }

  try {
    const data = JSON.parse(previewDataJson.value)
    const config = previewConfigJson.value ? JSON.parse(previewConfigJson.value) : {}

    console.log('开始渲染模板:', {
      template: editForm.value.optionTemplate,
      data,
      config,
    })

    const rendered = renderTemplate(editForm.value.optionTemplate, { data, config })
    console.log('模板渲染结果:', rendered)

    return rendered
  }
  catch (error) {
    console.error('模板渲染失败:', error)
    return null
  }
})

// 简单的模板渲染函数（支持基本的 Scriban 语法）
function renderTemplate(template: string, context: { data: any, config: any }): string {
  let result = template

  // 替换 {{ data.xxx }} 格式的变量
  result = result.replace(/\{\{\s*data\.([a-zA-Z_]\w*(?:\[\d+\])?)\s*\}\}/g, (match, fieldPath) => {
    try {
      // 处理数组索引访问，如 data.seriesNames[0]
      if (fieldPath.includes('[')) {
        const [fieldName, indexPart] = fieldPath.split('[')
        const index = Number.parseInt(indexPart.replace(']', ''))
        const value = context.data[fieldName]
        if (Array.isArray(value) && value[index] !== undefined) {
          return JSON.stringify(value[index])
        }
      }
      else {
        const value = context.data[fieldPath]
        if (value !== undefined) {
          return JSON.stringify(value)
        }
      }
    }
    catch (error) {
      console.warn('模板变量解析失败:', fieldPath, error)
    }
    return match
  })

  // 替换 {{ config.xxx }} 格式的变量
  result = result.replace(/\{\{\s*config\.([a-zA-Z_]\w*)\s*\}\}/g, (match, fieldName) => {
    const value = context.config[fieldName]
    return value !== undefined ? JSON.stringify(value) : match
  })

  // 替换简单的 data.xxx 格式的变量（不带花括号）
  result = result.replace(/(?<!\{)\bdata\.([a-zA-Z_]\w*)\b(?!\})/g, (match, fieldName) => {
    const value = context.data[fieldName]
    return value !== undefined ? JSON.stringify(value) : match
  })

  // 替换简单的 config.xxx 格式的变量（不带花括号）
  result = result.replace(/(?<!\{)\bconfig\.([a-zA-Z_]\w*)\b(?!\})/g, (match, fieldName) => {
    const value = context.config[fieldName]
    return value !== undefined ? JSON.stringify(value) : match
  })

  return result
}

// 生成预览数据
function generatePreviewData() {
  generatingData.value = true

  try {
    if (fieldMappings.value.length === 0) {
      message.warning('请先配置字段映射')
      return
    }

    // 根据字段映射生成示例数据
    const sampleData: Record<string, any> = {}
    const sampleConfig: Record<string, any> = {}

    fieldMappings.value.forEach((field) => {
      if (!field.name) {
        console.warn('跳过未命名字段:', field)
        return
      }

      if (field.configType === ConfigType.FieldMapping) {
        // 生成数据字段的示例数据
        sampleData[field.name] = generateSampleDataByType(field.dataType, field.name)
      }
      else if (field.configType === ConfigType.StyleConfig) {
        // 生成样式配置的示例数据
        sampleConfig[field.name] = generateSampleConfigByType(field.dataType, field.name)
      }
    })

    console.log('生成的示例数据:', { sampleData, sampleConfig })

    previewDataJson.value = JSON.stringify(sampleData, null, 2)
    previewConfigJson.value = JSON.stringify(sampleConfig, null, 2)

    if (Object.keys(sampleData).length === 0 && Object.keys(sampleConfig).length === 0) {
      message.warning('未生成任何数据，请检查字段映射配置')
    }
    else {
      message.success(`示例数据生成成功 (数据字段: ${Object.keys(sampleData).length}, 配置字段: ${Object.keys(sampleConfig).length})`)
    }
  }
  catch (error) {
    console.error('生成示例数据失败:', error)
    message.error('生成示例数据失败')
  }
  finally {
    generatingData.value = false
  }
}

// 根据数据类型生成示例数据
function generateSampleDataByType(dataType: FieldDataType, fieldName?: string | null): any {
  const lowerFieldName = (fieldName || '').toLowerCase()

  switch (dataType) {
    case FieldDataType.String:
      if (lowerFieldName.includes('name') || lowerFieldName.includes('category')) {
        return ['类别A', '类别B', '类别C', '类别D', '类别E']
      }
      return '示例文本'

    case FieldDataType.Number:
      return Math.floor(Math.random() * 100) + 1

    case FieldDataType.Boolean:
      return true

    case FieldDataType.StringArray:
      if (lowerFieldName.includes('month') || lowerFieldName.includes('time')) {
        return ['1月', '2月', '3月', '4月', '5月', '6月']
      }
      return ['选项1', '选项2', '选项3', '选项4', '选项5']

    case FieldDataType.NumberArray:
      return Array.from({ length: 6 }, () => Math.floor(Math.random() * 100) + 1)

    case FieldDataType.ObjectArray:
      if (lowerFieldName.includes('series')) {
        return [
          { name: '系列1', data: [120, 132, 101, 134, 90, 230] },
          { name: '系列2', data: [220, 182, 191, 234, 290, 330] },
          { name: '系列3', data: [150, 232, 201, 154, 190, 330] },
        ]
      }
      return [
        { key: '键1', value: '值1' },
        { key: '键2', value: '值2' },
        { key: '键3', value: '值3' },
      ]

    default:
      return null
  }
}

// 根据数据类型生成示例配置
function generateSampleConfigByType(dataType: FieldDataType, fieldName?: string | null): any {
  const lowerFieldName = (fieldName || '').toLowerCase()

  switch (dataType) {
    case FieldDataType.String:
      if (lowerFieldName.includes('title')) {
        return '示例图表标题'
      }
      if (lowerFieldName.includes('color')) {
        return '#1890ff'
      }
      return '示例配置'

    case FieldDataType.Number:
      if (lowerFieldName.includes('size') || lowerFieldName.includes('width') || lowerFieldName.includes('height')) {
        return 400
      }
      return 12

    case FieldDataType.Boolean:
      return true

    case FieldDataType.StringArray:
      if (lowerFieldName.includes('color')) {
        return ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
      }
      return ['配置1', '配置2', '配置3']

    case FieldDataType.NumberArray:
      return [10, 20, 30, 40, 50]

    default:
      return null
  }
}

// 更新预览
function updatePreview() {
  updatingPreview.value = true

  try {
    // 触发计算属性重新计算
    nextTick(() => {
      if (renderedChartConfig.value) {
        message.success('预览更新成功')
      }
      else {
        message.warning('请检查模板配置和预览数据')
      }
    })
  }
  catch (error) {
    console.error('更新预览失败:', error)
    message.error('更新预览失败')
  }
  finally {
    updatingPreview.value = false
  }
}

// 为树形表格添加唯一key
let fieldMappingIdCounter = 0
function generateFieldMappingId() {
  return `field_${++fieldMappingIdCounter}`
}

// 创建新的字段映射
function createFieldMapping(): FieldMappingWithKey {
  const mapping: FieldMappingWithKey = {
    key: generateFieldMappingId(),
    name: null,
    displayName: null,
    dataType: FieldDataType.String,
    configType: ConfigType.FieldMapping,
    child: [],
    children: undefined,
  }
  return mapping
}

// 判断字段是否可以有子字段
function canHaveChildren(record: FieldMappingWithKey) {
  if (!record || record.dataType === undefined) {
    return false
  }
  return record.dataType === FieldDataType.ObjectArray || record.dataType === FieldDataType.StringArray || record.dataType === FieldDataType.NumberArray
}

// 添加根字段映射
function addFieldMapping() {
  fieldMappings.value.push(createFieldMapping())
}

// 添加子字段
function addChildField(parentRecord: FieldMappingWithKey) {
  if (!parentRecord || !parentRecord.key) {
    console.error('Invalid parent record:', parentRecord)
    return
  }

  if (!parentRecord.children) {
    parentRecord.children = []
  }

  const childMapping = createFieldMapping()
  parentRecord.children.push(childMapping)

  // 确保父节点展开
  if (!expandedKeys.value.includes(parentRecord.key)) {
    expandedKeys.value.push(parentRecord.key)
  }

  // 触发响应式更新
  fieldMappings.value = [...fieldMappings.value]

  // 强制更新树组件
  nextTick(() => {
    // 选中新添加的子字段
    selectedField.value = childMapping
  })
}

// 递归删除字段映射
function removeFieldMapping(record: FieldMappingWithKey) {
  if (!record || !record.key) {
    console.error('Invalid record:', record)
    return
  }

  const removeFromArray = (mappings: FieldMappingWithKey[], targetKey: string): boolean => {
    const index = mappings.findIndex(mapping => mapping.key === targetKey)
    if (index !== -1) {
      mappings.splice(index, 1)
      return true
    }

    for (const mapping of mappings) {
      if (mapping.children && removeFromArray(mapping.children, targetKey)) {
        return true
      }
    }
    return false
  }

  // 如果删除的是当前选中的字段，清空选中状态
  if (selectedField.value?.key === record.key) {
    selectedField.value = null
  }

  // 如果删除的字段有子字段，也要清空相关的选中状态
  const clearSelectedIfChild = (field: FieldMappingWithKey) => {
    if (selectedField.value?.key === field.key) {
      selectedField.value = null
    }
    if (field.children) {
      field.children.forEach(clearSelectedIfChild)
    }
  }
  clearSelectedIfChild(record)

  // 从展开keys中移除
  const removeExpandedKeys = (field: FieldMappingWithKey) => {
    const index = expandedKeys.value.indexOf(field.key)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    }
    if (field.children) {
      field.children.forEach(removeExpandedKeys)
    }
  }
  removeExpandedKeys(record)

  removeFromArray(fieldMappings.value, record.key)
}

// 数据类型变化时的处理
function onDataTypeChange(record: FieldMappingWithKey) {
  if (!record) {
    console.error('Invalid record:', record)
    return
  }
  // 如果新的数据类型不支持子字段，清空子字段
  if (!canHaveChildren(record)) {
    record.children = undefined
    record.child = []
  }
}

// 选择字段
function selectField(field: FieldMappingWithKey) {
  if (!field || !field.key) {
    console.error('Invalid field:', field)
    return
  }
  selectedField.value = field
}

// 更新字段显示（当字段名称或显示名称改变时）
function updateFieldDisplay() {
  // 触发响应式更新
  fieldMappings.value = [...fieldMappings.value]
}

// 树展开事件处理
function onTreeExpand(expandedKeysValue: string[]) {
  expandedKeys.value = expandedKeysValue
}

// 初始化展开所有节点
function initExpandedKeys() {
  const keys: string[] = []
  const collectKeys = (mappings: FieldMappingWithKey[]) => {
    mappings.forEach((mapping) => {
      keys.push(mapping.key)
      if (mapping.children && mapping.children.length > 0) {
        collectKeys(mapping.children)
      }
    })
  }
  collectKeys(fieldMappings.value)
  expandedKeys.value = keys
}

// 获取数据类型标签
function getDataTypeLabel(dataType: FieldDataType): string {
  const labels = {
    [FieldDataType.String]: '字符串',
    [FieldDataType.Number]: '数字',
    [FieldDataType.Boolean]: '布尔值',
    [FieldDataType.StringArray]: '字符串数组',
    [FieldDataType.NumberArray]: '数字数组',
    [FieldDataType.ObjectArray]: '对象数组',
  }
  return labels[dataType] || '未知'
}

// 获取数据类型颜色
function getDataTypeColor(dataType: FieldDataType): string {
  const colors = {
    [FieldDataType.String]: 'blue',
    [FieldDataType.Number]: 'green',
    [FieldDataType.Boolean]: 'orange',
    [FieldDataType.StringArray]: 'purple',
    [FieldDataType.NumberArray]: 'cyan',
    [FieldDataType.ObjectArray]: 'red',
  }
  return colors[dataType] || 'default'
}

// 获取配置类型标签
function getConfigTypeLabel(configType: ConfigType): string {
  const labels = {
    [ConfigType.FieldMapping]: '字段映射',
    [ConfigType.StyleConfig]: '样式配置',
  }
  return labels[configType] || '未知'
}

// 获取配置类型颜色
function getConfigTypeColor(configType: ConfigType): string {
  const colors = {
    [ConfigType.FieldMapping]: 'geekblue',
    [ConfigType.StyleConfig]: 'volcano',
  }
  return colors[configType] || 'default'
}

// 判断是否为子字段
function isChildField(field: FieldMappingWithKey | null): boolean {
  if (!field)
    return false
  // 通过遍历所有字段来判断是否为子字段
  const findInChildren = (mappings: FieldMappingWithKey[]): boolean => {
    for (const mapping of mappings) {
      if (mapping.children) {
        if (mapping.children.some(child => child.key === field.key)) {
          return true
        }
        if (findInChildren(mapping.children)) {
          return true
        }
      }
    }
    return false
  }
  return findInChildren(fieldMappings.value)
}

// 从配置模板智能生成字段配置
function generateFieldsFromTemplate() {
  if (!editForm.value.optionTemplate) {
    message.warning('请先输入ECharts配置模板')
    return
  }

  try {
    // 解析模板中的字段引用
    const template = editForm.value.optionTemplate
    const dataFields = new Set<string>()
    const configFields = new Set<string>()

    // 匹配 data.fieldName 格式的字段引用
    const dataFieldRegex = /data\.([a-zA-Z_]\w*)/g
    let match = dataFieldRegex.exec(template)
    while (match !== null) {
      if (match[1]) {
        dataFields.add(match[1])
      }
      match = dataFieldRegex.exec(template)
    }

    // 匹配 config.fieldName 格式的字段引用
    const configFieldRegex = /config\.([a-zA-Z_]\w*)/g
    match = configFieldRegex.exec(template)
    while (match !== null) {
      if (match[1]) {
        configFields.add(match[1])
      }
      match = configFieldRegex.exec(template)
    }

    if (dataFields.size === 0 && configFields.size === 0) {
      message.warning('未在模板中找到 data.xxx 或 config.xxx 格式的字段引用')
      return
    }

    // 清空现有字段配置
    fieldMappings.value = []

    // 为 data 字段创建字段映射配置
    dataFields.forEach((fieldName) => {
      const field = createFieldMapping()
      field.name = fieldName
      field.displayName = getDisplayNameFromFieldName(fieldName)
      field.dataType = guessDataTypeFromFieldName(fieldName)
      field.configType = ConfigType.FieldMapping

      // 如果是复杂字段，添加子字段
      if (field.dataType === FieldDataType.ObjectArray) {
        addCommonChildFields(field, fieldName)
      }

      fieldMappings.value.push(field)
    })

    // 为 config 字段创建样式配置
    configFields.forEach((fieldName) => {
      const field = createFieldMapping()
      field.name = fieldName
      field.displayName = getDisplayNameFromFieldName(fieldName)
      field.dataType = guessConfigDataType(fieldName)
      field.configType = ConfigType.StyleConfig
      fieldMappings.value.push(field)
    })

    const totalFields = dataFields.size + configFields.size
    message.success(`成功生成 ${totalFields} 个字段配置 (${dataFields.size} 个数据字段, ${configFields.size} 个样式配置)`)

    // 初始化展开所有节点
    nextTick(() => {
      initExpandedKeys()
    })
  }
  catch {
    message.error('解析模板失败，请检查模板格式')
  }
}

// 根据字段名猜测显示名称
function getDisplayNameFromFieldName(fieldName: string): string {
  const nameMap: Record<string, string> = {
    x: 'X轴字段',
    xField: 'X轴字段',
    xAxis: 'X轴字段',
    y: 'Y轴字段',
    yField: 'Y轴字段',
    yAxis: 'Y轴字段',
    series: '系列字段',
    seriesField: '系列字段',
    value: '数值字段',
    valueField: '数值字段',
    values: '数值数组',
    name: '名称字段',
    nameField: '名称字段',
    category: '分类字段',
    categoryField: '分类字段',
    data: '数据字段',
    dataField: '数据字段',
    title: '标题',
    chartTitle: '图表标题',
    color: '颜色配置',
    colors: '颜色配置',
    backgroundColor: '背景颜色',
    textColor: '文字颜色',
    fontSize: '字体大小',
    fontFamily: '字体',
    width: '宽度',
    height: '高度',
    margin: '边距',
    padding: '内边距',
  }

  const lowerFieldName = fieldName.toLowerCase()
  for (const [key, value] of Object.entries(nameMap)) {
    if (lowerFieldName.includes(key.toLowerCase())) {
      return value
    }
  }

  return fieldName
}

// 根据字段名猜测数据类型（用于 data 字段）
function guessDataTypeFromFieldName(fieldName: string): FieldDataType {
  const lowerFieldName = fieldName.toLowerCase()

  if (lowerFieldName.includes('series') || lowerFieldName === 'data') {
    return FieldDataType.ObjectArray
  }
  if (lowerFieldName.includes('colors') || lowerFieldName.includes('values') || lowerFieldName === 'x' || lowerFieldName === 'y') {
    return FieldDataType.StringArray
  }
  if (lowerFieldName.includes('value') || lowerFieldName.includes('number') || lowerFieldName.includes('count')) {
    return FieldDataType.Number
  }

  return FieldDataType.String
}

// 根据字段名猜测配置数据类型（用于 config 字段）
function guessConfigDataType(fieldName: string): FieldDataType {
  const lowerFieldName = fieldName.toLowerCase()

  if (lowerFieldName.includes('colors') || lowerFieldName.includes('values')) {
    return FieldDataType.StringArray
  }
  if (lowerFieldName.includes('size') || lowerFieldName.includes('width') || lowerFieldName.includes('height')
    || lowerFieldName.includes('margin') || lowerFieldName.includes('padding')) {
    return FieldDataType.Number
  }
  if (lowerFieldName.includes('show') || lowerFieldName.includes('enable') || lowerFieldName.includes('visible')) {
    return FieldDataType.Boolean
  }

  return FieldDataType.String
}

// 为复杂字段添加常见的子字段
function addCommonChildFields(parentField: FieldMappingWithKey, fieldName: string) {
  if (!parentField.children) {
    parentField.children = []
  }

  const lowerFieldName = fieldName.toLowerCase()

  if (lowerFieldName.includes('series')) {
    // 系列字段的常见子字段
    const nameChild = createFieldMapping()
    nameChild.name = 'name'
    nameChild.displayName = '系列名称'
    nameChild.dataType = FieldDataType.String
    nameChild.configType = ConfigType.FieldMapping
    parentField.children.push(nameChild)

    const valueChild = createFieldMapping()
    valueChild.name = 'value'
    valueChild.displayName = '系列数值'
    valueChild.dataType = FieldDataType.NumberArray
    valueChild.configType = ConfigType.FieldMapping
    parentField.children.push(valueChild)
  }
  else if (lowerFieldName === 'data') {
    // 通用数据字段的子字段
    const keyChild = createFieldMapping()
    keyChild.name = 'key'
    keyChild.displayName = '键名'
    keyChild.dataType = FieldDataType.String
    keyChild.configType = ConfigType.FieldMapping
    parentField.children.push(keyChild)

    const valueChild = createFieldMapping()
    valueChild.name = 'value'
    valueChild.displayName = '键值'
    valueChild.dataType = FieldDataType.String
    valueChild.configType = ConfigType.FieldMapping
    parentField.children.push(valueChild)
  }
}

// 将 FieldMapping 转换为带 key 的树形数据
function convertToTreeData(mappings: FieldMapping[]): FieldMappingWithKey[] {
  return mappings.map((mapping) => {
    const item: FieldMappingWithKey = {
      ...mapping,
      key: generateFieldMappingId(),
      children: mapping.child && mapping.child.length > 0 ? convertToTreeData(mapping.child) : undefined,
    }
    return item
  })
}

// 将带 key 的树形数据转换回 FieldMapping
function convertToFieldMappings(treeData: FieldMappingWithKey[]): FieldMapping[] {
  return treeData.map((item) => {
    const mapping = new FieldMapping()
    mapping.name = item.name
    mapping.displayName = item.displayName
    mapping.dataType = item.dataType
    mapping.configType = item.configType
    mapping.child = item.children ? convertToFieldMappings(item.children) : []
    return mapping
  })
}

function openEditModal(record?: ChartTemplate) {
  if (record) {
    editForm.value = { ...record }
    fieldMappings.value = record.fieldMappings ? convertToTreeData(record.fieldMappings) : []
  }
  else {
    editForm.value = new ChartTemplate()
    fieldMappings.value = []
  }
  selectedField.value = null // 重置选中的字段
  expandedKeys.value = [] // 重置展开状态

  // 重置预览相关状态
  previewDataJson.value = ''
  previewConfigJson.value = ''
  generatingData.value = false
  updatingPreview.value = false

  editVisible.value = true
  activeTab.value = 'config'
  // 重置计数器，确保每次打开都有新的key
  fieldMappingIdCounter = 0

  // 初始化展开所有节点
  nextTick(() => {
    initExpandedKeys()
  })
}

async function handleEditOk() {
  try {
    if (!editForm.value.name) {
      message.warning('请填写模板名称')
      return
    }

    // 将字段映射数据转换并添加到表单中
    editForm.value.fieldMappings = convertToFieldMappings(fieldMappings.value)

    if (Guid.isNotNull(editForm.value.id)) {
      await ChartStatistices.UpdateTemplate_PostAsync(
        { id: String(editForm.value.id) },
        editForm.value as ChartTemplate,
      )
      message.success('更新成功')
    }
    else {
      await ChartStatistices.CreateTemplate_PostAsync(editForm.value as ChartTemplate)
      message.success('创建成功')
    }
    editVisible.value = false
    tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`操作失败: ${e.message || e}`)
  }
}

async function deleteTemplate(record: ChartTemplate) {
  try {
    await ChartStatistices.DeleteTemplate_PostAsync({ id: String(record.id) })
    message.success('删除成功')
    tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`删除失败: ${e.message || e}`)
  }
}

function previewTemplate(record: ChartTemplate) {
  Modal.info({
    title: '模板预览',
    content: `模板名称: ${record.name}`,
    width: 800,
  })
}
</script>

<style scoped>
.field-node {
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.field-node:hover {
  background-color: #f5f5f5;
}

.field-node:hover .ant-btn {
  opacity: 1;
}

.field-node .ant-btn {
  opacity: 0;
  transition: opacity 0.2s;
}

/* 树形组件样式优化 */
:deep(.ant-tree .ant-tree-node-content-wrapper) {
  width: 100%;
}

:deep(.ant-tree .ant-tree-title) {
  width: 100%;
}

/* 选中状态样式 */
:deep(.ant-tree .ant-tree-node-selected .field-node) {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

/* 自定义树形图标样式 */
.field-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

/* 树形组件图标颜色 */
:deep(.custom-tree .ant-tree-iconEle) {
  color: #1890ff;
}

/* 树形连接线样式 */
:deep(.custom-tree .ant-tree-indent-unit) {
  width: 20px;
}

:deep(.custom-tree .ant-tree-switcher) {
  color: #1890ff;
}

/* 树形节点悬停效果 */
:deep(.custom-tree .ant-tree-node-content-wrapper:hover) {
  background-color: transparent;
}
</style>
